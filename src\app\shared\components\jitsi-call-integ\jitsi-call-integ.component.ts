import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';
import { EndCallTypesEnum } from 'src/app/core/enums/end-call-types-enum.enum';
import { RoleEnum } from 'src/app/core/enums/role-enum.enum';
import { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';
import { ICallData } from 'src/app/core/interfaces/calls/icall-data';
import { IEndCallSessionRequest } from 'src/app/core/interfaces/calls/iend-call-session-request';
import { IJoinCallSessionRequest } from 'src/app/core/interfaces/calls/ijoin-call-session-request';
import { IStartCallSessionRequest } from 'src/app/core/interfaces/calls/istart-call-session-request';
import { IStartCallSessionResponse } from 'src/app/core/interfaces/calls/istart-call-session-response';
import { IJitsiSettingOptions } from 'src/app/core/interfaces/calls/jitsi-call-interfaces/ijitsi-setting-options';
import { AlertifyService } from 'src/app/core/services/alertify-services/alertify.service';
import { CallsService } from 'src/app/core/services/calls-services/calls.service';
import { JitsiIntegService } from 'src/app/core/services/jitsi-integ-services/jitsi-integ.service';
import { SocketService } from 'src/app/core/services/soket/socket/socket.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-jitsi-call-integ',
  templateUrl: './jitsi-call-integ.component.html',
  styleUrls: ['./jitsi-call-integ.component.scss']
})
export class JitsiCallIntegComponent implements OnInit {

  // @Output() sardTolabEvent:EventEmitter

  @Output() endCallEvent = new EventEmitter<string>();
  @Input() jitsiSettingOptions: IJitsiSettingOptions | undefined;
  @Input() callInitModel: IStartCallSessionRequest | undefined;
  @Input() callJoinModel: IJoinCallSessionRequest | undefined;
  callId?: string;

  startSessionObj: IStartCallSessionResponse | undefined;

  constructor(private callService: CallsService,
    private alertify: AlertifyService,
    private jitsiService: JitsiIntegService,
    private router: Router,
    private _socketService: SocketService
  ) { }

  ngOnInit(): void {
    this.callJoiner();
    this._socketService.on(environment.socketEvents.CALL_CANCELED, () => {
      debugger;
      this.endCall(this.callId, true, this.callInitModel?.callModuleTypehuffazId);
    });
  }

  callJoiner() {
    switch (this.jitsiSettingOptions?.startCallType) {
      case StartCallTypesEnum.StartMode:
        this.startCallSession();
        break;
      case StartCallTypesEnum.JoinMode:
        this.joinCallSession();
        break;
    }

  }

  startCallSession() {
    this.callService.startCallSession(this.callInitModel || {}).subscribe(async res => {
      if (res.isSuccess) {
        this.startSessionObj = res.data as IStartCallSessionResponse;
        this._socketService.emit(environment.socketEvents.JOIN_ROOM, this.startSessionObj.socketRoom);
        this.callId = this.startSessionObj.callId;
        await this.jitsiService.joinCall(this.startSessionObj, this.jitsiSettingOptions?.isVideoMute);
        this.fireEventJitsi();
      }
      else {
        this.alertify.error(res.message || '');
      }
    }, error => {
      //logging
    })
  }


  joinCallSession() {
    this.callService.joinCallSession(this.callJoinModel || {}).subscribe(async res => {
      if (res.isSuccess) {
        let rst = res.data as IStartCallSessionResponse;
        this.callId = res.data.callId;
        await this.jitsiService.joinCall(rst, this.jitsiSettingOptions?.isVideoMute);
        this.fireEventJitsi();
      }
      else {
        this.alertify.error(res.message || '');
      }
    }, error => {
      //logging
    })
  }

  fireEventJitsi() {
    this.jitsiService.api.addEventListeners({
      readyToClose: this.handleVideoConferenceLeft,
      participantLeft: this.handleParticipantLeft
    });
  }

  handleVideoConferenceLeft = () => {
    switch (this.jitsiSettingOptions?.endCallType) {
      case EndCallTypesEnum.TasmeeaTskStudModrEndCall:
        this.endCall(this.callId, true, this.callInitModel?.callModuleTypehuffazId);
        this.endCallEvent.emit(this.callId);
        break;
      case EndCallTypesEnum.TasmeeaTskTechUsrEndCall:
        this.endCall(this.callId, false, this.callJoinModel?.callModuleTypehuffazId);
        this.endCallEvent.emit(this.callId);
        break;
      case EndCallTypesEnum.SardTolabTskStudModrEndCall:
        this.endCall(this.callId, true, this.callInitModel?.callModuleTypehuffazId);
        this.endCallEvent.emit(this.callId);
        break;
      case EndCallTypesEnum.SardTolabTskStudUsrEndCall:
        this.endCall(this.callId, false, this.callJoinModel?.callModuleTypehuffazId);
        this.endCallEvent.emit(this.callId);
        break;
      case EndCallTypesEnum.FreeRecitationStudModrEndCall:
        this.endCall(this.callId, false, this.callInitModel?.callModuleTypehuffazId);
        this.endCallEvent.emit(this.callId);
        break;
      case EndCallTypesEnum.FreeRecitationTechUsrEndCall:
        this.endCall(this.callId, false, this.callJoinModel?.callModuleTypehuffazId);
        this.endCallEvent.emit(this.callId);
        break;

    }
  }
  handleParticipantLeft = async (participant: any) => {
    // any one will close the call the other one will leave
    //this.handleVideoConferenceLeft();
 }
  endCall(callId?: string, isMod?: boolean, type?: CallTypesEnum) {
    let recitEndCall: IEndCallSessionRequest = {
      callId: callId,
      callModuleTypehuffazId: type,
      isMod: isMod,
      usrId: JSON.parse(localStorage.getItem("user") || '{}').id
    }
    this.callService.endCallSession(recitEndCall).subscribe(res => {
      if (res.isSuccess) {

      }
    }, error => {
      //logging
    });
  }

}
