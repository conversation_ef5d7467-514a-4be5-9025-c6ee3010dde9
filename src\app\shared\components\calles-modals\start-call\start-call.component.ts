import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CallOperators } from 'src/app/core/enums/call-operators.enum';
import { CallStatusEnum } from 'src/app/core/enums/call-status-enum.enum';
import { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { IProgStuRecitationTskCallConnector } from 'src/app/core/interfaces/calls/prog-tasks/iprog-stu-recitation-tsk-call-connector';
import { IProgTasmeeaTskCallConnector } from 'src/app/core/interfaces/calls/prog-tasks/iprog-tasmeea-tsk-call-connector';
import { RingtoneService } from 'src/app/core/services/calls-services/ringtone/ringtone.service';


@Component({
  selector: 'app-start-call',
  templateUrl: './start-call.component.html',
  styleUrls: ['./start-call.component.scss']
})
export class StartCallComponent implements OnInit {

currentUser: IUser | undefined;
  calleeName: any;
private get callDataTyped():
  IProgStuRecitationTskCallConnector | IProgTasmeeaTskCallConnector {
  return this.data.callData;
}
  constructor(
    public dialogRef: MatDialogRef<StartCallComponent>,
    private ringtoneService: RingtoneService,
    @Inject(MAT_DIALOG_DATA)
    public data: { callData: IProgStuRecitationTskCallConnector | IProgTasmeeaTskCallConnector; role: CallTypesEnum }
  ) {}

  ngOnInit(): void {
    this.currentUser = JSON.parse(localStorage.getItem('user') || '{}');

  switch (this.data.role) {
    case CallTypesEnum.StudentRecitation:
      const recitationCall = this.callDataTyped as IProgStuRecitationTskCallConnector;
      this.calleeName = recitationCall.availableStuInfo?.studNameAr;
      break;

    case CallTypesEnum.Recitation:
      const tasmeeaCall = this.callDataTyped as IProgTasmeeaTskCallConnector;
      this.calleeName = tasmeeaCall.availableTechInfo?.techNameAr;
      break;
  }    this.startCall();
  }

  startCall(): void {
    this.ringtoneService.play(CallOperators.Sender);
  }

  cancelCall(): void {
    this.ringtoneService.stop(CallOperators.Sender);
    this.dialogRef.close(CallStatusEnum.Canceled);
      this.callStateService.setCallStatus(CallStatusEnum.Canceled);

  }
}
