import { Component, OnInit, ViewChild } from '@angular/core';
import { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';
import { IProgStuRecitationTskCallConnector } from 'src/app/core/interfaces/calls/prog-tasks/iprog-stu-recitation-tsk-call-connector';
import { IProgTasmeeaTskCallConnector } from 'src/app/core/interfaces/calls/prog-tasks/iprog-tasmeea-tsk-call-connector';
import { IProgramDayTasksModel } from 'src/app/core/interfaces/programs-interfaces/iprogram-day-tasks-model';
import { IStudentProgramDutiesResponse } from 'src/app/core/interfaces/student-program-duties-interfaces/istudent-program-duties-response';
import { StudentProgramDutyDaysTaskComponent } from './student-program-duty-days-task/student-program-duty-days-task.component';
import { StudentProgramDutyDaysComponent } from './student-program-duty-days/student-program-duty-days.component';
import { SoketListenerService } from 'src/app/core/services/soket/soket-listener/soket-listener.service';
import { MatDialog } from '@angular/material/dialog';
import { StartCallComponent } from 'src/app/shared/components/calles-modals/start-call/start-call.component';
import { CallStatusEnum } from 'src/app/core/enums/call-status-enum.enum';
import { JitsiCallInterviewComponent } from 'src/app/shared/components/jitsi-call-interview/jitsi-call-interview.component';

@Component({
  selector: 'app-student-prog-duties',
  templateUrl: './student-prog-duties.component.html',
  styleUrls: ['./student-prog-duties.component.scss'],
})
export class StudentProgDutiesComponent implements OnInit {
  @ViewChild(StudentProgramDutyDaysTaskComponent) progDayTaskChild:
    | StudentProgramDutyDaysTaskComponent
    | undefined;
  @ViewChild(StudentProgramDutyDaysComponent) progDayChild:
    | StudentProgramDutyDaysComponent
    | undefined;

  programDutyDay: IStudentProgramDutiesResponse | undefined;
  taskDetails?: IProgramDayTasksModel;
  openAddScientificProblem: boolean = false;
  progTasmeeaTskCallConn: IProgTasmeeaTskCallConnector | undefined;
  isShowCall: boolean = false;
  progStuRecitationTskCallConn: IProgStuRecitationTskCallConnector | undefined;
  role = CallTypesEnum.Recitation;
  dayOrder: Number | undefined;
  isShowEndingCallMessage: boolean | undefined;

  constructor(
    private dialog: MatDialog,
    ) {}

  ngOnInit(): void {}

  progDutyDayEventCallBk(event?: IStudentProgramDutiesResponse) {
    if (this.progDayTaskChild && event) {
      this.progDayTaskChild.isAnsweredIndex = 0;
      this.progDayTaskChild.defaultSelectedTask = 0;
      this.progDayTaskChild.programDutyDay = event;
      this.progDayTaskChild?.getProgramDutyDays();
      this.dayOrder = event.dayNum;
    }
    this.programDutyDay = event;
  }

  sendTaskIdToProgDayTaskDetails(item?: IProgramDayTasksModel) {
    this.taskDetails = item;
  }

  loadTask() {
    this.progDayTaskChild?.onTaskChange();
  }

  loadDay($event: boolean) {
    if ($event == true) {
      this.progDayChild?.onDayChange();
    } else {
      this.progDayTaskChild?.getProgramDutyDays();
    }
  }

  teacherCallPhon(event: IProgTasmeeaTskCallConnector) {
    this.progTasmeeaTskCallConn = event;
    this.isShowCall = true;
    this.role = CallTypesEnum.Recitation;
    this.startCall();
  }
  startCall() {
    const callData =
      this.role == CallTypesEnum.Recitation
        ? this.progTasmeeaTskCallConn
        : this.progStuRecitationTskCallConn;
    const dialogRef = this.dialog.open(StartCallComponent, {
      disableClose: true,
      backdropClass: 'custom-backdrop',
      data: { callData: callData, role: this.role },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result === CallStatusEnum.Rejected) {
        this.isShowEndingCallMessage = true;
      } else if (result === CallStatusEnum.Canceled) {
      }
    });
  }

  closeAddScientificProblem() {
    this.openAddScientificProblem = false;
  }

  openAddScientificProblemPopup() {
    this.openAddScientificProblem = true;
  }

  stuCallPhon(event: IProgStuRecitationTskCallConnector) {
    this.progStuRecitationTskCallConn = event;
    this.isShowCall = true;
    this.role = CallTypesEnum.StudentRecitation;
    this.startCall();
  }

  backToDuties(event: boolean) {
    this.isShowCall = false;
  }
}
