// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  //  baseUrl:  'http://**************:1020/api/',
  // baseUrl: 'http://*************:8080/api/',
  baseUrl: 'http://localhost:5000/api/',
   clientIdGoogleAuth:'870269866675-itennv2jic75v6g773igda4iun5fi75e.apps.googleusercontent.com',
   appIdFacebookAuth:'476016400500617',
     // Socket server URL
  socketUrl: 'http://localhost:3000',

  // Audio URLs
  audioFiles: {
    reciever: 'assets/audios/ringing1.mp3',
    sender: 'assets/audios/ringtone.mp3'
  },

  // baseUrl: 'http://*************:1050/api/',
  //  baseUrl: 'http://localhost:5000/api/',
  // baseUrl: 'http://*************:1050/api/',

  //staging
  firebaseConfig : {
    apiKey: "AIzaSyBz6L0qamX2o9J8APMJ2QlKF5jDhECicI4",
    authDomain: "hoffaz.firebaseapp.com",
    databaseURL: "https://hoffaz-default-rtdb.firebaseio.com",
    projectId: "hoffaz",
    storageBucket: "hoffaz.appspot.com",
    messagingSenderId: "870269866675",
    appId: "1:870269866675:web:8eaf3bca7841a83b52d6c6",
    measurementId: "G-CG7X412XTV"
  },

    socketEvents: {
    IDENTIFY_USER: 'identify-user',
    NOTIFY_CALLEE_TO_JOIN: 'notify_callee_to_join',
    CALL_ACCEPTED: 'call_accepted',
    CALL_REJECTED: 'call_rejected',
    CALL_CANCELED: 'call_canceled',
    RECEIVING_CALL: 'receiving_call',
    CALL_NOT_ANSWERED:'call_not_answered',
    USER_JOINED_CALL: 'user_joined_call',
    FORCE_JOIN_CALL: 'force_join_call',
    HOST_DISCONNECTED: 'host-disconnected',
    CLIENT_DISCONNECTED: 'client-disconnected',
    CONNECT: 'connect',
    JOIN_ROOM:'joinRoom'
  },

  //dev
  // firebaseConfig: {
  //   apiKey: "AIzaSyAasCgSTMGQnw0lsMD5mcYn34qTKI7oPU8",
  //   authDomain: "huffaz-web.firebaseapp.com",
  //   databaseURL: "https://huffaz-web-default-rtdb.firebaseio.com",
  //   projectId: "huffaz-web",
  //   storageBucket: "huffaz-web.appspot.com",
  //   messagingSenderId: "108442615117",
  //   appId: "1:108442615117:web:069f47a1aff30837814b04",
  //   measurementId: "G-NHN6W6KE4L"
  // }


  // prod 
  // firebaseConfig: {
  //   apiKey: "AIzaSyAasCgSTMGQnw0lsMD5mcYn34qTKI7oPU8",
  //   authDomain: "huffaz-web.firebaseapp.com",
  //   databaseURL: "https://huffaz-web-default-rtdb.firebaseio.com",
  //   projectId: "huffaz-web",
  //   storageBucket: "huffaz-web.appspot.com",
  //   messagingSenderId: "108442615117",
  //   appId: "1:108442615117:web:069f47a1aff30837814b04",
  //   measurementId: "G-NHN6W6KE4L"
  // },
   firebasePushNotifKeyPair : "BG_xOP9wizD5kgiKaUIsO3qs9bHANNEZTlh5u4rQuMebc62BfG27qPH_T36l4wfAhV-854FfkXZb0AnDto9RSoU",
   jitsiCall :'huffadh.arabdt.com/'//'testcall.mostanir.com',
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
