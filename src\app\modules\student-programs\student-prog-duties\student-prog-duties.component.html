<div class="container-fluid">
  <div class="row" *ngIf="!isShowCall">
    <div class="col-3">
      <app-student-program-duty-days
        (progDutyDayEvent)='progDutyDayEventCallBk($event)'></app-student-program-duty-days>
    </div>
    <div class="col-3">
      <app-student-program-duty-days-task *ngIf='programDutyDay' [programDutyDay]='programDutyDay'
        (taskDetailsEvent)="sendTaskIdToProgDayTaskDetails($event)"
        (taskIsEndEvent)="loadDay($event)"></app-student-program-duty-days-task>
    </div>
    <div class="col-6" *ngIf='taskDetails && programDutyDay'>
      <app-student-program-duty-days-task-details [taskDetails]='taskDetails' [dayOrder]='programDutyDay.dayNum'
        (taskIsSaveEvent)=" loadTask()" (teacherCallPhonEvent)="teacherCallPhon($event)"
        (openAddScientificProblem)="openAddScientificProblemPopup()"
        (stuCallPhonEvent)="stuCallPhon($event)"></app-student-program-duty-days-task-details>
    </div>

    <div class="overlay" *ngIf="openAddScientificProblem">
      <app-add-task-scientific-problem (closeAddScientificProblem)="closeAddScientificProblem()"
        [dutyDayFromParent]="programDutyDay" [dayTaskFromParent]="taskDetails"></app-add-task-scientific-problem>
    </div>
  </div>

</div>
<div class="container-fluid ">
  <ng-container *ngIf="isShowCall">

    <app-student-recitation-call [tskId]='taskDetails?.id' [progTasmeeaTskCallConn]='progTasmeeaTskCallConn'
      [progStuRecitationTskCallConn]='progStuRecitationTskCallConn' [user]='role'
      (backToDaysListAndDuties)='backToDuties($event)'>
    </app-student-recitation-call>

    <ng-container *ngIf="isShowEndingCallMessage">
      <div class=" alert alert-danger">
        {{ 'CALLING.PLEASE_END_CALL' | translate }}
      </div>
    </ng-container>
  </ng-container>
</div>