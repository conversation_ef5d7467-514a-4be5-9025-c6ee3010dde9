import { AlertifyService } from './../../../../../core/services/alertify-services/alertify.service';
import { TranslateService } from '@ngx-translate/core';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { LanguageEnum } from 'src/app/core/enums/language-enum.enum';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { IGroupExplanationsTeacherViewRequest } from 'src/app/core/interfaces/calls/groupExplanation/igroup-explanations-teacher-view-request';
import { IGroupExplanationsTeacherViewResponse } from 'src/app/core/interfaces/calls/groupExplanation/igroup-explanations-teacher-view-response';
import { IGroupCallConnector } from 'src/app/core/interfaces/calls/groupExplanation/igroup-call-connector';
import { StartCallTypesEnum } from 'src/app/core/enums/start-call-types-enum.enum';
import { RecitationExplanationGroupService } from 'src/app/core/services/calls-services/recitationExplanationGroup/recitation-explanation-group.service';

@Component({
  selector: 'app-teacher-recitation-groups',
  templateUrl: './teacher-recitation-groups.component.html',
  styleUrls: ['./teacher-recitation-groups.component.scss']
})
export class TeacherRecitationGroupsComponent implements OnInit {
  @Output() showAddGroup = new EventEmitter<boolean>();
  @Output() stuCallPhonEvent = new EventEmitter<IGroupCallConnector>();
  @Output() sendGroupId = new EventEmitter<string>();

  groupExplanationsTeacherViewRequest: IGroupExplanationsTeacherViewRequest = { skip: 0, take: 2147483647 }
  responseList: IGroupExplanationsTeacherViewResponse[] = [];
  currentUser: IUser | undefined;
  langEnum = LanguageEnum;
  selectedIndex = 0;
  groupCallConnector: IGroupCallConnector | undefined;
  constructor(
    private groupExplanationServices: RecitationExplanationGroupService,
    public translate: TranslateService,
    private alertify: AlertifyService,
  ) { }

  ngOnInit(): void {
    this.currentUser = JSON.parse(localStorage.getItem("user") as string) as IUser;
    this.getTeacherViewtGroupExplanation()
  }

  addNewGroup() {
    this.showAddGroup.emit(true)
  }

  getTeacherViewtGroupExplanation() {
    this.groupExplanationsTeacherViewRequest.techId = this.currentUser?.id

    this.groupExplanationServices.getTeacherViewtGroupExplanation(this.groupExplanationsTeacherViewRequest).subscribe(res => {
      if (res.isSuccess) {
        this.responseList = res.data;
        if (this.responseList && this.responseList.length > 0) {
          this.sendIdForDetailsGroupExplanation(this.responseList[0].id)

        }
        else {
          this.sendIdForDetailsGroupExplanation(null)
        }

      }
      else {
        this.alertify.error(res.message || "");
      }
    }, error => {
//logging

    })
  }
  filterByText(searchKey: string) {
    this.groupExplanationsTeacherViewRequest.name = searchKey;
    this.getTeacherViewtGroupExplanation();
  }

  sendIdForDetailsGroupExplanation(Id: any) {
    this.sendGroupId.emit(Id)

  }
  stuCall(isVideoMute?: boolean, groupInfo?: IGroupExplanationsTeacherViewResponse) {
    this.groupCallConnector = {
      groupInfo: groupInfo,
      isVideoMute: isVideoMute,
      startCallType: StartCallTypesEnum.StartMode
    }
    this.stuCallPhonEvent.emit(this.groupCallConnector)
  }

}

