import { Injectable } from '@angular/core';
import { CallOperators } from 'src/app/core/enums/call-operators.enum';
import { IncommingCallComponent } from 'src/app/shared/components/calles-modals/incomming-call/incomming-call.component';
import { RingtoneService } from '../../calls-services/ringtone/ringtone.service';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { SocketConnectionService } from '../socket-connection/socket-connection.service';
import { CallStatusEnum } from 'src/app/core/enums/call-status-enum.enum';
import { environment } from 'src/environments/environment';
import { ICallData } from 'src/app/core/interfaces/calls/icall-data';

@Injectable({
  providedIn: 'root',
})
export class SoketListenerService {
    private callStatusSubject = new BehaviorSubject<CallStatusEnum | null>(null);
    callStatus$ = this.callStatusSubject.asObservable();
  constructor(
    private dialog: MatDialog,
    private ringtoneService: RingtoneService,
    private socketConnectionService: SocketConnectionService,
    private router: Router
  ) {}

  listenToSocketEvents(): void {
    const socket = this.socketConnectionService.getSocket();
    socket.on(environment.socketEvents.RECEIVING_CALL, (data:ICallData) => {
      this.ringtoneService.play(CallOperators.Reciever);
      this.dialog
        .open(IncommingCallComponent, {
          data,
          disableClose: true,
          panelClass: 'incoming-call-dialog',
          hasBackdrop: false,
        })
        .afterClosed()
        .subscribe((result) => {
          this.ringtoneService.stop(CallOperators.Reciever);
            console.log('call accepted', data);
          if (result === 'accepted') {
            socket.emit(environment.socketEvents.CALL_ACCEPTED, data);
            this.routToCall(data);
          } else if (result === 'rejected') {
            socket.emit(environment.socketEvents.CALL_REJECTED, data);
          }
        });
    });

    socket.on(environment.socketEvents.CALL_ACCEPTED, (data: ICallData) => {
      this.ringtoneService.stop(CallOperators.Sender);
      console.log('call accepted', data);
      this.dialog.closeAll();
      socket.emit(environment.socketEvents.NOTIFY_CALLEE_TO_JOIN, data);
    });

    socket.on(environment.socketEvents.CALL_REJECTED, () => {
      this.ringtoneService.stopAll();
      this.dialog.openDialogs.forEach((dialogRef) => {
        dialogRef.close(CallStatusEnum.Rejected);
      });
    });
    socket.on(environment.socketEvents.CALL_NOT_ANSWERED, () => {
      console.log('call not answered');
      this.ringtoneService.stopAll();
      this.dialog.openDialogs.forEach((dialogRef) => {
        dialogRef.close(CallStatusEnum.NotAnswered);
      });
      this.dialog.closeAll();
    });

    socket.on(environment.socketEvents.HOST_DISCONNECTED, () => {
      console.warn('⚠️ Socket disconnected');
    });
  }
  routToCall(data: ICallData) {
      this.ringtoneService.stopAll();
      this.dialog.closeAll();
      const usrId = data.ToUserId;
      const callId = data.CallId;
      const TaskId = data.TaskId;
      const day = data.DayId;
      const batId = data.ProgBatchId;
      const callModuleTypehuffazId = data.ModuleTypeNum;
      const modId = data.Moderator;
      const isVideoMute = data.IsVideoMute ?? false;
      const path = `recitation-task-call/call/${day}/${usrId}/${callId}/${TaskId}/${callModuleTypehuffazId}/${modId}/${isVideoMute}/${batId}`;
      this.router.navigateByUrl(path);
  }
    setCallStatus(status: CallStatusEnum) {
    this.callStatusSubject.next(status);
  }
}
